from django.db import models
from datetime import datetime

class DataForm(models.Model):
    data1 = models.CharField(max_length=100)
    data2 = models.CharField(max_length=100)
    submit_time = models.CharField(max_length=100)  # Change submit_time field to <PERSON><PERSON><PERSON><PERSON>

    def save(self, *args, **kwargs):
        if not self.submit_time:
            self.submit_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')  # Convert current time to string
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.data1} - {self.data2}"

