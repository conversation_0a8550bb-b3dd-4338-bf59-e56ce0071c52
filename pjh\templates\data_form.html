<!DOCTYPE html>
{% load static %}
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>馆藏图书排架号数据收集系统</title>
    <link rel="stylesheet" href="{% static 'css/picnic.min.css' %}">
    <style>
        body {
            padding: 1em;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .card {
            padding: 1.5em;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            background: white;
        }
        .error-message {
            color: #e74c3c;
            font-size: 1.2em;
            margin: 0.5em 0;
            text-align: center;
            width: 100%;
            display: block;
        }
        /* 新增按钮样式 */
        .submit-button {
            padding: 8px 24px;
            font-size: 18px;
            min-width: 120px;
        }
        .success-message {
            color: #27ae60;
            font-size: 1.2em;
            margin: 0.5em 0;
            text-align: center;  /* 新增居中样式 */
            width: 100%;  /* 确保宽度足够 */
            display: block;  /* 确保块级显示 */
        }
        .data-table {
            width: 100%;
            margin-top: 2em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1 class="text-center">馆藏图书排架号数据收集系统</h1>
            <form id="dataForm" name="dataform" method="post" action="">
                {% csrf_token %}
                <label for="data1">排架号:</label>
                <input type="text" name="data1" id="data1" maxlength="8" onkeydown='handleKeyPress(event, "data1")'>
                
                <label for="data2">财产号:</label>
                <input type="text" name="data2" id="data2" maxlength="10" onkeydown='handleKeyPress(event, "data2")'>
                
                <div class="flex two">
                     <div>
                        <button type="button" class="success submit-button" onclick="checkAndSubmit()">提交</button>
                    </div>
                  
                </div>
                <p class="small">使用扫码枪全程无需键鼠。键盘敲回车可换行和提交。</p>
            </form>
        </div>
    </div>

    <div id="submittedData"></div>
    <div id="successMessage" class="success-message" style="display: none;"></div>
    <div id="errorMessages" class="error-message" style="display: none;"></div>

    <div class="container">
        <h4><a href="{% url 'download_data_page' %}" target="_blank">前往数据下载页面</a></h4>
    </div>

    <!-- 添加一个新的部分来显示最新的100条数据 -->
    <div class="container">
        <div class="card">
            <h4>最新的100条数据</h4>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>排架号</th>
                        <th>财产号</th>
                        <th>提交时间</th>
                    </tr>
                </thead>
                <tbody id="latestDataBody">
                    {% for data in latest_data %}
                    <tr>
                        <td>{{ data.data1 }}</td>
                        <td>{{ data.data2 }}</td>
                        <td>{{ data.submit_time }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
<script>
function clearFormData() {
    document.getElementById('data1').value = '';
    document.getElementById('data2').value = '';
    document.getElementById('data1').focus(); // 将焦点设置到data1输入框
}

let enterKeyPressCount = 0;

function handleKeyPress(event, field) {
    if (event.keyCode === 13) {
        event.preventDefault(); // 阻止回车键默认提交行为

        // 增加回车键按下的次数
        enterKeyPressCount++;

        // 检查是否按下了3次回车键
        if (enterKeyPressCount === 2) {
            // 重置计数器
            enterKeyPressCount = 0;

            // 调用提交函数
            checkAndSubmit();
        } else {
            // 如果不是第3次回车，根据field的值切换焦点
            if (field === 'data1') {
                document.getElementById('data2').focus();
            }
        }
    }
}

function checkAndSubmit() {
    checkData(); // 首先检查数据
    
    let errorMessages = document.getElementById('errorMessages');
    let submittedData = document.getElementById('submittedData');

    if (errorMessages.innerHTML === '') {
      // 清空之前提交的数据信息
      submittedData.innerHTML = '';
      
        submitData(); // 若无错误则提交数据
    } else {
        // 如果有错误，显示错误消息并清空提交信息
       submittedData.innerHTML = '';
       errorMessages.style.display = 'block';
    }
}

function checkData() {
    let data1 = document.getElementById('data1').value;
    let data2 = document.getElementById('data2').value;
    
    let errorMessages = document.getElementById('errorMessages');
    errorMessages.innerHTML = '';
    
    if (data1 === '') {
        errorMessages.innerHTML += '<p class="error">错误：排架号不能为空</p>';
    }
    
    if (data2 === '') {
        errorMessages.innerHTML += '<p class="error">错误：财产号不能为空</p>';
    }
    
    if (data1 !== '' && data1.charAt(0) !== 'A') {
        errorMessages.innerHTML += '<p class="error">错误：排架号必须以"A"开头</p>';
    }
    if (data2 !== '' && !/^\d+$/.test(data2)) {
    errorMessages.innerHTML += '<p class="error">错误：财产号必须为纯数字</p>';
    }
    if (data1 === data2) {
        errorMessages.innerHTML += '<p class="error">错误：财产号和排架号不能相同</p>';
    }
    
    if (data1 !== '') {
    if (data1.length !== 8) {
        errorMessages.innerHTML += '<p class="error">错误：排架号长度必须为8位</p>';
    }
    if (data1.charAt(0).toUpperCase() !== 'A') {
        errorMessages.innerHTML += '<p class="error">错误：排架号必须以"A"开头</p>';
    }
    if (!/^\d+$/.test(data1.slice(1))) {
        errorMessages.innerHTML += '<p class="error">错误：A后必须为7位数字</p>';
    }
 }

    if (data2.length < 8 || data2.length > 10) {
        errorMessages.innerHTML += '<p class="error">错误：财产号必须是8到10位字符</p>';
    }
        // 财产号中不能有空格
    if (data2.includes(' ')) {
        errorMessages.innerHTML += '<p class="error">错误：财产号中不能有空格</p>';
    }

    if (errorMessages.innerHTML !== '') {
        errorMessages.style.display = 'block'; // 显示错误消息
    } else {
        errorMessages.style.display = 'none'; // 隐藏错误消息
    }
}

function submitData() {
    let data1 = document.getElementById('data1').value;
    let data2 = document.getElementById('data2').value;
    
    fetch('/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': '{{ csrf_token }}'
        },
        body: `data1=${data1}&data2=${data2}`
    })
    .then(response => response.text())
    .then(data => {
      document.getElementById('submittedData').innerHTML = `<br><span style="color: blue; font-size: 24px; text-align: center; display: block;">排架号:<b> ${data1}</b>, 财产号: <b>${data2}</b> 提交成功！</span><br>`;
      
      // 清空文本框
      clearFormData();
      
      // 更新最新的100条数据
      updateLatestData();
      
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

// 新增函数：更新最新的100条数据
function updateLatestData() {
    fetch('/get_latest_data/') // 假设你有一个视图函数来处理这个请求
    .then(response => response.json())
    .then(data => {
        const latestDataBody = document.getElementById('latestDataBody');
        latestDataBody.innerHTML = ''; // 清空现有的数据
        
        data.forEach(item => {
            const row = `<tr>
                <td>${item.data1}</td>
                <td>${item.data2}</td>
                <td>${item.submit_time}</td>
            </tr>`;
            latestDataBody.innerHTML += row;
        });
    })
    .catch(error => {
        console.error('Error:', error);
    });
}
</script>
</body>
</html>
<div class="container">
    {% if user.is_authenticated %}
        <p>欢迎, {{ user.username }}! <a href="{% url 'logout' %}">登出</a></p>
    {% else %}
        <p><a href="{% url 'login' %}">登录</a></p>
    {% endif %}
</div>