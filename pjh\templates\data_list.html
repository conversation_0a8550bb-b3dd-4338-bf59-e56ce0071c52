<!DOCTYPE html>
{% load static %}
<html>
<head>
    <title>数据维护</title>
    <link rel="stylesheet" href="{% static 'css/picnic.min.css' %}">
    <style>
        body {
            padding: 1em;
            background-color: #f5f5f5;
        }
        .message {
            padding: 1em;
            margin-bottom: 1em;
            border-radius: 4px;
            color: red;  /* 修改字体颜色为红色 */
            font-weight: bold;  /* 加粗字体 */
        }
        .message.warning {
            background-color: #ff9800;
        }
        .message.error {
            background-color: #ff4444;
        }
        .message.success {
            background-color: #4CAF50;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .card {
            padding: 1.5em;
            margin-bottom: 1em;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            background: white;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            box-shadow: 0 2px 3px rgba(0,0,0,0.1);
        }
        th {
            background-color: #42b983;
            color: white;
            font-weight: bold;
            padding: 12px;
            text-align: left;
        }
        td {
            padding: 10px 12px;
            border-bottom: 1px solid #e6e6e6;
        }
        tr:nth-child(even) {
            background-color: #f8f8f8;
        }
        tr:hover {
            background-color: #f0f7ff;
        }
        .error {
            color: #ff4444;
            font-weight: bold;
        }
        .button.active {
            background: #42b983;
            color: white;
        }
        .button.warning {
            background: #ff9800;
        }
        .button.success {
            background: #4CAF50;
        }
        .button.danger {
            background: #ff4444;
        }

       
    </style>
</head>
<body>
    <div class="container">
       

        <!-- 查询功能卡片 -->
        <div class="card">
            <div class="search-form">
                <b>数据查询：</b>  
                <form method="GET">
                    <select name="search_field" style="padding: 8px; margin-right: 10px;">
                        <option value="data1" {% if search_field == 'data1' %}selected="selected"{% endif %}>排架号查询</option>
                        <option value="data2" {% if search_field == 'data2' %}selected="selected"{% endif %}>财产号查询</option>
                    </select>
                    <input type="text" 
                           name="search_key" 
                           placeholder="输入查询关键词"
                           value="{{ search_key }}">
                    <button type="submit" class="button">🔍 查询</button>
                    <a href="{% url 'data_list' %}" class="button" style="background: #666">本页面重置</a>
                </form>
            </div>
        </div>

        <!-- 数据表格卡片 -->
        <div class="card">
            <div>
                <a href="?sort=asc" class="button{% if current_sort == 'asc' %} active{% endif %}">升序排列</a>
                <a href="?sort=desc" class="button{% if current_sort == 'desc' %} active{% endif %}">降序排列</a>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>排架号</th>
                        <th>财产号</th>
                        <th>提交时间</th>
                    </tr>
                </thead>
                <tbody>
            {% if is_comparing %}
                <tr class="compare-header">
                    <td colspan="4" style="background: #f8f9fa; font-weight: bold;">
                        发现 {{ data|length }} 条与系统不符的财产号 ↓
                    </td>
                </tr>
            {% endif %}

            {% for item in data %}
            <tr>
                <td>{{ item.id }}</td>
                <td>{{ item.data1 }}</td>
                <td class="{% if is_comparing %}error{% endif %}">
                    {{ item.data2 }}
                </td>
                <td>{{ item.submit_time }}</td>
            </tr>
            {% empty %}
                {% if is_comparing %}
                <tr>
                    <td colspan="4" style="color: #28a745; font-weight: bold;">
                        ✅ 所有财产号均符合
                    </td>
                </tr>
                {% else %}
                <tr>
                    <td colspan="4">No data available</td>
                </tr>
                {% endif %}
            {% endfor %}
        </tbody>
            </table>
        </div>
        
        <!-- 数据操作卡片 -->
        <div class="card">
            <div class="add-form">
                <!-- 重复项检查 -->
                <div style="margin: 5px 0;">
                    <b>重复项检查：</b>
                    <a href="?duplicate_type=both&sort={{ current_sort }}"
                       class="button{% if duplicate_type == 'both' %} active{% endif %}">
                       检查排架号+财产号重复
                    </a>
                    <a href="?duplicate_type=data1&sort={{ current_sort }}"
                       class="button{% if duplicate_type == 'data1' %} active{% endif %}">
                       仅检查排架号重复
                    </a>
                    <a href="?duplicate_type=data2&sort={{ current_sort }}"
                       class="button{% if duplicate_type == 'data2' %} active{% endif %}">
                       仅检查财产号重复
                    </a>
                     <form method="POST" style="display: inline;">
                        {% csrf_token %}
                        <button type="submit" name="deduplicate_both"
                                class="button"
                                style="background: #FF9800"
                                onclick="return confirm('警告：此操作将永久删除重复数据！确定要继续吗？')">
                            去重排架号+财产号重复（保留最早记录）
                        </button>
                    </form>
                </div>
            
            <!-- 错误财产号检查 -->
            <div style="margin: 5px 0;">
                 <b>错误财产号检查：</b><a href="?check_missing=1"
                   class="button{% if is_comparing %} active{% endif %}"
                   style="background: #CC27B0">
                    财产号检查（比对馆藏财产号）
                </a> 点击后请耐心等待！
            </div>
            </div>
        </div> 
            <!-- 新增数据卡片 -->
            <div class="card">
                <div class="add-form">
                    <b>新增数据（不限财产号格式）：</b>   
                    <form method="POST" autocomplete="off">
                        {% csrf_token %}
                        <input type="text" name="new_data1" 
                               placeholder="输入排架号" 
                               pattern="^A\d{7}$"
                               title="请输入字母A后跟7位数字，例如A0001234"
                               required>
                        <input type="text" name="new_data2"
                               placeholder="输入财产号"
                               required>
                        <button type="submit" class="button">提交新数据</button>
                    </form>
                </div>
            </div>

            <!-- 删除数据卡片 -->
            <div class="card">
                <div class="delete-form">
                    <b>数据删除（先查询要删除的数据，输入其ID号）：</b>   
                    <form method="POST">
                        {% csrf_token %}
                        <input type="text" name="delete_id" 
                               placeholder="输入要删除的ID号" 
                               required>
                        <button type="submit" class="button" 
                                style="background: #ff4444">删除数据</button>
                    </form>
                </div>
            </div>
            
<h4><a href="{% url 'data_form' %}">返回主页面</a></h4>
       
</div>  

 <!-- 消息提示 -->
<script>
  {% for message in messages %}
    alert('消息：{{ message|escapejs }}');
  {% endfor %}
</script> 
</body>
</html>