<!DOCTYPE html>
{% load static %}

<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>下载数据</title>
    <link rel="stylesheet" href="{% static 'css/picnic.min.css' %}">
    <style>
        body {
            padding: 1em;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .card {
            padding: 1.5em;
            margin-bottom: 1em;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            background: white;
        }
        .messages {
            margin: 15px 0;
            padding: 10px;
            border-radius: 5px;
            background-color: #ffeeee;
            border: 1px solid #ffcccc;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>数据下载页面（两种方式任选一个）</h1>
            
            <!-- 下载全部数据 -->
            <div>
                <h2>下载全部数据(点击后请耐心等待)</h2>
                <form method="get" action="/">
                    <input type="hidden" name="download_excel" value="1">
                    <button type="submit" class="success">点击下载全部数据</button>
                </form>
            </div>
            
            <div class="card">
                <h2>排架号范围</h2>
                <div class="flex two">
                    <div>
                        <label>起始排架号:</label>
                        <input type="text" id="start_data" name="start_data" maxlength="8" required 
                               pattern="A\d{7}" title="格式：A后跟7位数字（如A0001234）">
                    </div>
                    <div>
                        <label>结束排架号:</label>
                        <input type="text" id="end_data" name="end_data" maxlength="8" required 
                               pattern="A\d{7}" title="格式：A后跟7位数字（如A0001234）">
                    </div>
                </div>
                <small>格式：A0001234</small>
            </div>

            <div class="card">
                <h2>数据下载选项</h2>
                <div class="flex two">
                    <button onclick="downloadData()" class="success">下载以上范围内的数据</button>
                    <button onclick="downloadMissingData()" class="warning">下载以上范围内未录入的排架号</button>
                </div>
            </div>

            <!-- 错误消息显示 -->
            {% if messages %}
            <div class="messages">
                {% for message in messages %}
                <div class="error">⚠ {{ message }}</div>
                {% endfor %}
            </div>
            {% endif %}

            <div class="flex two">
                <a href="{% url 'data_list' %}" target="_blank" class="button">前往数据维护页面</a>
                <a href="{% url 'data_form' %}" class="button">返回主页面</a>
            </div>
        </div>
    </div>

    <script>
document.addEventListener('DOMContentLoaded', function() {
    // 为所有排架号输入框添加输入限制
    const inputs = document.querySelectorAll('input[name^="start_data1"], input[name^="end_data1"]');
    inputs.forEach(input => {
        input.addEventListener('input', function(e) {
            this.value = this.value.toUpperCase().replace(/[^A0-9]/g, '');
            if (this.value.length > 1) {
                this.value = 'A' + this.value.slice(1).replace(/\D/g, '');
            }
        });
    });
});

function validateInputs() {
    const startData = document.getElementById('start_data').value;
    const endData = document.getElementById('end_data').value;
    const regex = /^A\d{7}$/;

    if (!startData || !endData) {
        alert('请填写起始和结束排架号');
        return false;
    }

    if (!regex.test(startData) || !regex.test(endData)) {
        alert('排架号格式不正确，必须为A后跟7位数字');
        return false;
    }

    // 检查起始值是否小于结束值
    const startNum = parseInt(startData.slice(1), 10);
    const endNum = parseInt(endData.slice(1), 10);
    if (startNum > endNum) {
        alert('起始值不能大于结束值');
        return false;
    }

    return true;
}

function downloadData() {
    if (validateInputs()) {
        const start = document.getElementById('start_data').value;
        const end = document.getElementById('end_data').value;
        window.location.href = `/download_range_data/?start_data1=${encodeURIComponent(start)}&end_data1=${encodeURIComponent(end)}`;
    }
}

function downloadMissingData() {
    if (validateInputs()) {
        const start = document.getElementById('start_data').value;
        const end = document.getElementById('end_data').value;
        window.location.href = `/download_missing_data/?start_data1=${encodeURIComponent(start)}&end_data1=${encodeURIComponent(end)}`;
    }
}
</script>
</body>
</html>