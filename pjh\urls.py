from django.contrib.auth import views as auth_views
from django.urls import path
from . import views

urlpatterns = [
    path('', views.data_form, name='data_form'),
    path('get_latest_data/', views.get_latest_data, name='get_latest_data'),
    path('download_range_data/', views.download_range_data, name='download_range_data'),
    path('download_data/', views.download_data_page, name='download_data_page'),
    path('data/', views.data_list, name='data_list'),
    path('download_missing_data/', views.download_missing_data, name='download_missing_data'),
    path('login/', auth_views.LoginView.as_view(template_name='login.html'), name='login'),
    path('logout/', auth_views.LogoutView.as_view(), name='logout'),
]
