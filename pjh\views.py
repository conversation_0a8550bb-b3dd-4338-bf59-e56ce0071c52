from django.shortcuts import render, redirect
from .models import DataForm
from django.http import HttpResponse, HttpResponseRedirect
from django.views.decorators.csrf import ensure_csrf_cookie
import pandas as pd
from datetime import datetime
from django.http import JsonResponse
from django.contrib import messages
from django.urls import reverse
from django.db import models
from django.db import connection
from django.contrib.auth.decorators import login_required
import re

def get_latest_data(request):
    latest_data = DataForm.objects.all().order_by('-id')[:100].values('data1', 'data2', 'submit_time')
    return JsonResponse(list(latest_data), safe=False)
    


def clean_data(value):
    """清除非法字符的函数"""
    if isinstance(value, str):
        # 删除非法字符（包括 NULL 和不可见字符）
        return re.sub(r'[\x00-\x1F\x7F-\x9F]', '', value)
    return value


def clean_data(value):
    # 假设这是你的数据清理函数（根据实际情况修改）
    return str(value).strip() if value else value

@login_required
def download_range_data(request):
    if request.method == 'GET':
        start_data1 = request.GET.get('start_data1')
        end_data1 = request.GET.get('end_data1')
        
        # 1. 检查参数是否存在
        if not start_data1 or not end_data1:
            messages.error(request, "必须填写起始值和结束值")
            return HttpResponseRedirect(reverse('download_data_page'))
        
        # 2. 检查参数格式是否正确（A + 7位数字）
        def is_valid_format(value):
            return len(value) == 8 and value[0] == 'A' and value[1:].isdigit()
        
        if not is_valid_format(start_data1) or not is_valid_format(end_data1):
            messages.error(request, "数据格式必须为 A 后跟 7 位数字（如 A0001234）")
            return HttpResponseRedirect(reverse('download_data_page'))
        
        # 3. 提取数字部分并转换为整数
        try:
            start_num = int(start_data1[1:])  # 提取 A 后面的数字部分
            end_num = int(end_data1[1:])
        except ValueError:
            messages.error(request, "数据格式无效")
            return HttpResponseRedirect(reverse('download_data_page'))
        
        # 4. 查询数据库
        queryset = DataForm.objects.filter(data1__regex=r'^A\d{7}$')  # 确保格式为 A + 7位数字
        queryset = queryset.filter(data1__gte=f'A{start_num:07d}', data1__lte=f'A{end_num:07d}')
        
        if not queryset.exists():  # 检查是否存在数据
            messages.error(request, f"在 {start_data1} 到 {end_data1} 范围内未找到数据")
            return HttpResponseRedirect(reverse('download_data_page'))
        
        # 5. 处理数据
        data = queryset.values('data1', 'data2', 'submit_time')
        df = pd.DataFrame(data)
        
        # 清理数据并去重
        df = df.applymap(clean_data)
        df = df.drop_duplicates(subset=['data1', 'data2', 'submit_time'])
        
        if df.empty:  # 检查数据是否为空
            messages.error(request, "数据经过清理后为空")
            return HttpResponseRedirect(reverse('download_data_page'))
        
        # 6. 生成文件
        df.sort_values(by='submit_time', ascending=False, inplace=True)
        filename = f"data_{start_data1}_to_{end_data1}.xlsx"
        
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            headers={'Content-Disposition': f'attachment; filename="{filename}"'},
        )
        # 指定自定义表头（方式一）
        custom_headers = ['排架号', '财产号', '提交时间']
        df.to_excel(response, index=False, header=custom_headers)

# 或者使用重命名列的方式（方式二）
# df.rename(columns={'data1': '数据1编号', 'data2': '数据2内容', 'submit_time': '提交时间'}, inplace=True)
# df.to_excel(response, index=False)

        return response

@login_required
def download_data_page(request):
    return render(request, 'download_data.html')

@login_required
def download_missing_data(request):
    if request.method == 'GET':
        start_data1 = request.GET.get('start_data1')
        end_data1 = request.GET.get('end_data1')

        # 参数校验
        if not start_data1 or not end_data1:
            messages.error(request, "必须填写起始值和结束值")
            return HttpResponseRedirect(reverse('download_data_page'))

        # 格式校验函数（复用已有逻辑）
        def is_valid_format(value):
            return len(value) == 8 and value[0] == 'A' and value[1:].isdigit()

        if not is_valid_format(start_data1) or not is_valid_format(end_data1):
            messages.error(request, "数据格式必须为 A 后跟 7 位数字（如 A0001234）")
            return HttpResponseRedirect(reverse('download_data_page'))

        # 提取数字部分
        try:
            start_num = int(start_data1[1:])
            end_num = int(end_data1[1:])
        except ValueError:
            messages.error(request, "数据格式无效")
            return HttpResponseRedirect(reverse('download_data_page'))
        # 在提取数字部分后添加
        MAX_RANGE = 60000
        if (end_num - start_num) > MAX_RANGE:
            messages.error(request, f"查询范围过大，最多支持{MAX_RANGE}个排架号")
            return HttpResponseRedirect(reverse('download_data_page'))
            
        # 范围校验
        if start_num > end_num:
            messages.error(request, "起始值不能大于结束值")
            return HttpResponseRedirect(reverse('download_data_page'))

        # 生成所有可能的排架号
        all_numbers = range(start_num, end_num + 1)
        all_data1 = [f"A{num:07d}" for num in all_numbers]

        # 查询存在的排架号
        existing_data = DataForm.objects.filter(data1__in=all_data1).values_list('data1', flat=True)
        existing_set = set(existing_data)

        # 获取缺失数据
        missing_data = [data1 for data1 in all_data1 if data1 not in existing_set]

        if not missing_data:
            messages.error(request, "该范围内所有排架号均已存在")
            return HttpResponseRedirect(reverse('download_data_page'))

        # 创建DataFrame并排序
        df = pd.DataFrame({'缺失的排架号': missing_data})
        df['num_part'] = df['缺失的排架号'].str[1:].astype(int)
        df.sort_values('num_part', inplace=True)
        df.drop('num_part', axis=1, inplace=True)

        # 生成响应
        filename = f"missing_{start_data1}_to_{end_data1}.xlsx"
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            headers={'Content-Disposition': f'attachment; filename="{filename}"'},
        )
        df.to_excel(response, index=False)
        return response
  

@login_required
def data_form(request):
    # 处理表单提交逻辑
    if request.method == 'POST':
        data1 = request.POST.get('data1')
        data2 = request.POST.get('data2')
        # 获得当前系统时间并转换为字符串
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        DataForm.objects.create(data1=data1, data2=data2, submit_time=current_time)

    # 下载 Excel文件
    if request.method == 'GET' and 'download_excel' in request.GET:
        data = DataForm.objects.all().values('data1', 'data2', 'submit_time')
        df = pd.DataFrame(data)
    
        # 清理非法字符
        df = df.applymap(clean_data)
    
        # 去除重复值
        df = df.drop_duplicates(subset=['data1', 'data2', 'submit_time'])
    
        # 按submit_time倒序排列
        df = df.sort_values(by='submit_time', ascending=False)
    
        response = HttpResponse(content_type='application/vnd.ms-excel')
        response['Content-Disposition'] = 'attachment; filename="pjh_cch.xlsx"'
    
        # 将排序后的DataFrame写入Excel文件
        #df.to_excel(response, index=False)
        
        # 指定自定义表头（方式一）
        custom_headers = ['排架号', '财产号', '提交时间']
        df.to_excel(response, index=False, header=custom_headers)

# 或者使用重命名列的方式（方式二）
# df.rename(columns={'data1': '数据1编号', 'data2': '数据2内容', 'submit_time': '提交时间'}, inplace=True)
# df.to_excel(response, index=False)

        return response
        
    # 获取最新的100条数据
    latest_data = DataForm.objects.all().order_by('-id')[:100]
    
    # 将数据传递到模板
    return render(request, 'data_form.html', {'latest_data': latest_data})


def handle_exception(request, e, delete_id=None):
    if isinstance(e, ValueError):
        msg = f'错误：{str(e)}'
    elif isinstance(e, DataForm.DoesNotExist):
        msg = f'ID {delete_id} 不存在' if delete_id else '记录不存在'
    else:
        msg = f'操作失败：{str(e)}'
    messages.error(request, msg)

def redirect_with_params(request):
    params = request.GET.copy()
    url = f"{request.path}?{params.urlencode()}"
    return redirect(url)

@login_required
def data_list(request):
    # 处理POST请求
    if request.method == 'POST':
        # 优先处理去重请求
        if 'deduplicate_both' in request.POST:
            try:
                # 找出需要保留的记录（每个data1+data2组合的最小ID）
                keep_ids = DataForm.objects.values('data1', 'data2').annotate(
                    min_id=models.Min('id')
                ).values_list('min_id', flat=True)

                # 删除重复记录
                deleted_count, _ = DataForm.objects.exclude(id__in=keep_ids).delete()

                if deleted_count == 0:
                    messages.info(request, "没有需要去重的重复记录")
                else:
                    messages.success(request, f"已删除 {deleted_count} 条重复记录，保留最早数据")

            except Exception as e:
                messages.error(request, f"去重操作失败：{str(e)}")

            return redirect_with_params(request)

        # 处理删除请求
        elif 'delete_id' in request.POST:
            try:
                delete_id = request.POST.get('delete_id')
                if not delete_id:
                    raise ValueError("ID不能为空")

                record = DataForm.objects.get(id=int(delete_id))
                record.delete()
                messages.success(request, f'ID {delete_id} 删除成功')

            except Exception as e:
                handle_exception(request, e, delete_id)

            return redirect_with_params(request)

        # 最后处理新增请求
        else:
            try:
                data1 = request.POST.get('new_data1', '').strip()
                data2 = request.POST.get('new_data2', '').strip()

                if not data1 or not data2:
                    raise ValueError("数据不能为空")

                DataForm.objects.create(data1=data1, data2=data2)
                messages.success(request, '数据添加成功')

            except Exception as e:
                messages.error(request, f'添加失败：{str(e)}')

            return redirect_with_params(request)

    # GET请求处理逻辑
    # 以下是原有的handle_get_request内容整合
    check_missing = request.GET.get('check_missing')
    duplicate_type = request.GET.get('duplicate_type')
    search_key = request.GET.get('search_key', '')
    search_field = request.GET.get('search_field', 'data1')
    sort_order = request.GET.get('sort', 'desc')
    no_duplicates = False

    queryset = DataForm.objects.all()

    # 处理重复项查询
    if duplicate_type:
        from django.db.models import Count, Q

        if duplicate_type == 'both':
            dup_combinations = DataForm.objects.values('data1', 'data2').annotate(
                count=Count('id')
            ).filter(count__gt=1)

            if not dup_combinations.exists():
                no_duplicates = True
                queryset = DataForm.objects.none()
            else:
                q_objects = Q()
                for combo in dup_combinations:
                    q_objects |= Q(data1=combo['data1'], data2=combo['data2'])
                queryset = queryset.filter(q_objects)

        elif duplicate_type == 'data1':
            dup_data1 = DataForm.objects.values('data1').annotate(
                count=Count('id')
            ).filter(count__gt=1)

            if not dup_data1.exists():
                no_duplicates = True
                queryset = DataForm.objects.none()
            else:
                queryset = queryset.filter(
                    data1__in=dup_data1.values_list('data1', flat=True)
                )

        elif duplicate_type == 'data2':
            dup_data2 = DataForm.objects.values('data2').annotate(
                count=Count('id')
            ).filter(count__gt=1)

            if not dup_data2.exists():
                no_duplicates = True
                queryset = DataForm.objects.none()
            else:
                queryset = queryset.filter(
                    data2__in=dup_data2.values_list('data2', flat=True)
                )
    else:
        if search_key:
            filter_kwargs = {f'{search_field}__icontains': search_key}
            queryset = queryset.filter(**filter_kwargs)

    # 应用排序
    if sort_order == 'asc':
        queryset = queryset.order_by('data1')
    else:
        queryset = queryset.order_by('-data1')

    # 获取数据
    data = queryset if duplicate_type else queryset[:5]

    # 字段比对逻辑
    if check_missing:
        try:
            with connection.cursor() as cursor:
                # 执行原始SQL查询
                cursor.execute("""
                        SELECT id, data1, data2, submit_time 
                        FROM pjh_dataform 
                        WHERE data2 NOT IN (SELECT prop_no FROM cch)
                    """)
                # 将结果转换为字典列表
                columns = [col[0] for col in cursor.description]
                data = [
                    dict(zip(columns, row))
                    for row in cursor.fetchall()
                ]
                # 标记当前处于比对状态
                is_comparing = True
        except Exception as e:
            messages.error(request, f'数据库查询失败：{str(e)}')
            return redirect_with_params(request)
    else:
        # 保持原有查询逻辑
        is_comparing = False
        data = queryset if (duplicate_type or check_missing) else queryset[:5]

    return render(request, 'data_list.html', {
        'data': data,
        'is_comparing': is_comparing,  # 新增比对状态标识
        'current_sort': sort_order,
        'search_key': search_key,
        'search_field': search_field,
        'duplicate_type': duplicate_type,
        'no_duplicates': no_duplicates
    })